import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Brain,
  Search,
  Zap,
  TrendingUp,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Clock,
  Target,
  Sparkles,
  BarChart3,
  Settings,
  Play,
  Pause,
  Activity
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { useEnhancedSEOOptimizer } from '@/hooks/use-enhanced-seo-optimizer';
import { automatedContentGenerator } from '@/lib/content/automated-content-generator';
import { backgroundSEOService } from '@/lib/seo/background-seo-service';
import { dynamicAnalyticsService, AIMetrics, ContentMetrics } from '@/lib/analytics/dynamic-analytics-service';
import { analyticsSeeder } from '@/lib/analytics/analytics-seeder';

// Using interfaces from dynamic-analytics-service

interface AutomationSettings {
  autoGeneration: boolean;
  autoOptimization: boolean;
  generationInterval: number;
  batchSize: number;
}

const AISEOAutomationTab: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [contentStats, setContentStats] = useState<ContentMetrics>({
    totalBlogs: 0,
    totalTreks: 0,
    totalBookings: 0,
    totalTestimonials: 0,
    publishedThisMonth: 0,
    optimizedContent: 0,
    totalViews: 0,
    totalEngagement: 0
  });
  const [aiStats, setAiStats] = useState<AIMetrics>({
    totalGenerations: 0,
    totalOptimizations: 0,
    successfulGenerations: 0,
    successfulOptimizations: 0,
    avgProcessingTime: 0,
    tokensUsed: 0,
    generationsThisWeek: 0,
    optimizationsThisWeek: 0
  });
  const [automationSettings, setAutomationSettings] = useState<AutomationSettings>({
    autoGeneration: false,
    autoOptimization: true,
    generationInterval: 24,
    batchSize: 3
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [queueStatus, setQueueStatus] = useState({
    totalJobs: 0,
    pendingJobs: 0,
    processingJobs: 0,
    completedJobs: 0,
    failedJobs: 0,
    isProcessing: false
  });

  const {
    stats,
    health,
    checkHealth,
    isHealthy,
    isDegraded,
    isUnhealthy,
    successRate,
    averageProcessingTime
  } = useEnhancedSEOOptimizer();

  const loadDashboardData = useCallback(async () => {
    try {
      // Ensure analytics data is seeded
      await analyticsSeeder.seedIfEmpty();

      // Load all data using dynamic analytics service
      const [contentMetrics, aiMetrics] = await Promise.all([
        dynamicAnalyticsService.getContentMetrics(),
        dynamicAnalyticsService.getAIMetrics()
      ]);

      setContentStats(contentMetrics);
      setAiStats(aiMetrics);

      // Get queue status
      const status = backgroundSEOService.getQueueStatus();
      setQueueStatus(status);

    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      toast({
        title: "Error",
        description: "Failed to load dashboard data",
        variant: "destructive",
      });
    }
  }, []);

  useEffect(() => {
    loadDashboardData();
    const interval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [loadDashboardData]);

  const handleGenerateContent = async () => {
    setIsGenerating(true);
    const startTime = Date.now();

    try {
      await automatedContentGenerator.generateBlogPosts(automationSettings.batchSize);

      // Track successful generation
      const processingTime = Date.now() - startTime;
      await dynamicAnalyticsService.trackAIOperation(
        'content_generation',
        'success',
        processingTime,
        undefined,
        'blog',
        undefined,
        undefined,
        { batchSize: automationSettings.batchSize }
      );

      toast({
        title: "Success",
        description: `Generated ${automationSettings.batchSize} blog posts successfully`,
      });
      loadDashboardData();
    } catch (error) {
      console.error('Content generation failed:', error);

      // Track failed generation
      const processingTime = Date.now() - startTime;
      await dynamicAnalyticsService.trackAIOperation(
        'content_generation',
        'failed',
        processingTime,
        undefined,
        'blog',
        undefined,
        error instanceof Error ? error.message : 'Unknown error',
        { batchSize: automationSettings.batchSize }
      );

      toast({
        title: "Error",
        description: "Failed to generate content",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleOptimizeAll = async () => {
    setIsOptimizing(true);
    const startTime = Date.now();

    try {
      await backgroundSEOService.optimizeAllContent();

      // Track successful optimization start
      const processingTime = Date.now() - startTime;
      await dynamicAnalyticsService.trackAIOperation(
        'seo_optimization',
        'success',
        processingTime,
        undefined,
        undefined,
        undefined,
        undefined,
        { operation: 'bulk_optimization' }
      );

      toast({
        title: "Success",
        description: "Started optimization for all content",
      });
      loadDashboardData();
    } catch (error) {
      console.error('SEO optimization failed:', error);

      // Track failed optimization
      const processingTime = Date.now() - startTime;
      await dynamicAnalyticsService.trackAIOperation(
        'seo_optimization',
        'failed',
        processingTime,
        undefined,
        undefined,
        undefined,
        error instanceof Error ? error.message : 'Unknown error',
        { operation: 'bulk_optimization' }
      );

      toast({
        title: "Error",
        description: "Failed to start SEO optimization",
        variant: "destructive",
      });
    } finally {
      setIsOptimizing(false);
    }
  };

  const getHealthIcon = () => {
    if (isHealthy) return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (isDegraded) return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    return <AlertTriangle className="h-5 w-5 text-red-500" />;
  };

  const getHealthStatus = () => {
    if (isHealthy) return 'Healthy';
    if (isDegraded) return 'Degraded';
    return 'Unhealthy';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Brain className="h-8 w-8" />
            AI & SEO Automation
          </h2>
          <p className="text-muted-foreground">
            Automated content generation and SEO optimization powered by Google Gemini AI
          </p>
        </div>
        <div className="flex items-center gap-2">
          {getHealthIcon()}
          <span className="text-sm font-medium">{getHealthStatus()}</span>
          <Button onClick={loadDashboardData} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Content</p>
                <p className="text-2xl font-bold">{contentStats.totalBlogs + contentStats.totalTreks}</p>
              </div>
              <Target className="h-8 w-8 text-blue-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                {contentStats.totalBlogs} blogs, {contentStats.totalTreks} treks
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">SEO Optimized</p>
                <p className="text-2xl font-bold text-green-600">{contentStats.optimizedContent}</p>
              </div>
              <Search className="h-8 w-8 text-green-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                {contentStats.pendingOptimization} pending optimization
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                <p className="text-2xl font-bold text-blue-600">{aiStats.totalOptimizations > 0 ? ((aiStats.successfulOptimizations / aiStats.totalOptimizations) * 100).toFixed(1) : 100}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                {aiStats.totalOptimizations} total optimizations
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Generated This Week</p>
                <p className="text-2xl font-bold text-purple-600">{aiStats.generationsThisWeek}</p>
              </div>
              <Sparkles className="h-8 w-8 text-purple-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                AI-generated content
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="content-generation">Content Generation</TabsTrigger>
          <TabsTrigger value="seo-optimization">SEO Optimization</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Queue Status
                </CardTitle>
                <CardDescription>Current automation queue status</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Total Jobs</span>
                    <Badge variant="outline">{queueStatus.totalJobs}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Pending</span>
                    <Badge variant="secondary">{queueStatus.pendingJobs}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Processing</span>
                    <Badge variant="default">{queueStatus.processingJobs}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Completed</span>
                    <Badge variant="outline" className="text-green-600">{queueStatus.completedJobs}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Failed</span>
                    <Badge variant="destructive">{queueStatus.failedJobs}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Performance Metrics
                </CardTitle>
                <CardDescription>AI system performance statistics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">API Success Rate</span>
                    <Badge variant="outline">{(successRate || 0).toFixed(1)}%</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Avg Processing Time</span>
                    <Badge variant="secondary">{(averageProcessingTime || 0).toFixed(1)}s</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">SEO Score Average</span>
                    <Badge variant="outline" className="text-blue-600">{seoStats.averageScore}/10</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Last Optimization</span>
                    <Badge variant="secondary">
                      {seoStats.lastOptimized ?
                        new Date(seoStats.lastOptimized).toLocaleDateString() :
                        'Never'
                      }
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="content-generation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5" />
                AI Content Generation
              </CardTitle>
              <CardDescription>
                Generate high-quality blog posts about Nepal trekking and adventure tourism
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Generate Blog Posts</h4>
                  <p className="text-sm text-muted-foreground">
                    Create {automationSettings.batchSize} AI-generated blog posts about Nepal adventures
                  </p>
                </div>
                <Button
                  onClick={handleGenerateContent}
                  disabled={isGenerating}
                  className="flex items-center gap-2"
                >
                  {isGenerating ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <Sparkles className="h-4 w-4" />
                  )}
                  {isGenerating ? 'Generating...' : 'Generate Content'}
                </Button>
              </div>

              <div className="border-t pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Auto-Generation</h4>
                    <p className="text-sm text-muted-foreground">
                      Automatically generate content every {automationSettings.generationInterval} hours
                    </p>
                  </div>
                  <Button
                    variant={automationSettings.autoGeneration ? "default" : "outline"}
                    onClick={() => setAutomationSettings(prev => ({ ...prev, autoGeneration: !prev.autoGeneration }))}
                    className="flex items-center gap-2"
                  >
                    {automationSettings.autoGeneration ? (
                      <>
                        <Pause className="h-4 w-4" />
                        Disable Auto-Gen
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4" />
                        Enable Auto-Gen
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="seo-optimization" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                SEO Optimization
              </CardTitle>
              <CardDescription>
                Optimize all content for search engines using AI-powered analysis
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Optimize All Content</h4>
                  <p className="text-sm text-muted-foreground">
                    Run SEO optimization on all blog posts and trek packages
                  </p>
                </div>
                <Button
                  onClick={handleOptimizeAll}
                  disabled={isOptimizing}
                  className="flex items-center gap-2"
                >
                  {isOptimizing ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <Zap className="h-4 w-4" />
                  )}
                  {isOptimizing ? 'Optimizing...' : 'Optimize All'}
                </Button>
              </div>

              <div className="border-t pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Background Optimization</h4>
                    <p className="text-sm text-muted-foreground">
                      Automatically optimize content in the background
                    </p>
                  </div>
                  <Button
                    variant={automationSettings.autoOptimization ? "default" : "outline"}
                    onClick={() => setAutomationSettings(prev => ({ ...prev, autoOptimization: !prev.autoOptimization }))}
                    className="flex items-center gap-2"
                  >
                    {automationSettings.autoOptimization ? (
                      <>
                        <Pause className="h-4 w-4" />
                        Disable Auto-SEO
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4" />
                        Enable Auto-SEO
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Automation Settings
              </CardTitle>
              <CardDescription>
                Configure AI automation behavior and schedules
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Generation Interval (hours)</label>
                  <input
                    type="number"
                    min="1"
                    max="168"
                    value={automationSettings.generationInterval}
                    onChange={(e) => setAutomationSettings(prev => ({
                      ...prev,
                      generationInterval: parseInt(e.target.value) || 24
                    }))}
                    className="w-full px-3 py-2 border rounded-md"
                  />
                  <p className="text-xs text-muted-foreground">
                    How often to generate new content automatically
                  </p>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Batch Size</label>
                  <input
                    type="number"
                    min="1"
                    max="10"
                    value={automationSettings.batchSize}
                    onChange={(e) => setAutomationSettings(prev => ({
                      ...prev,
                      batchSize: parseInt(e.target.value) || 3
                    }))}
                    className="w-full px-3 py-2 border rounded-md"
                  />
                  <p className="text-xs text-muted-foreground">
                    Number of posts to generate per batch
                  </p>
                </div>
              </div>

              <div className="border-t pt-4">
                <Button onClick={() => {
                  toast({
                    title: "Settings Saved",
                    description: "Automation settings have been updated",
                  });
                }}>
                  Save Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AISEOAutomationTab;
