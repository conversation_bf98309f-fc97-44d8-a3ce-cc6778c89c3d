import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Eye, 
  Clock, 
  RefreshCw,
  FileText,
  Mountain,
  MessageSquare,
  Calendar,
  Target,
  Activity
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface ContentMetrics {
  totalBlogs: number;
  totalTreks: number;
  totalBookings: number;
  totalTestimonials: number;
  publishedThisMonth: number;
  optimizedContent: number;
}

interface RecentActivity {
  id: string;
  type: 'blog' | 'trek' | 'booking' | 'testimonial' | 'optimization';
  title: string;
  date: string;
  status: string;
}

interface TopContent {
  id: string;
  title: string;
  type: 'blog' | 'trek';
  views: number;
  engagement: number;
}

const AnalyticsPerformanceTab: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [contentMetrics, setContentMetrics] = useState<ContentMetrics>({
    totalBlogs: 0,
    totalTreks: 0,
    totalBookings: 0,
    totalTestimonials: 0,
    publishedThisMonth: 0,
    optimizedContent: 0
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [topContent, setTopContent] = useState<TopContent[]>([]);

  const loadAnalyticsData = useCallback(async () => {
    setLoading(true);
    try {
      // Get content statistics from database
      const [blogsRes, treksRes, bookingsRes, testimonialsRes, seoLogsRes] = await Promise.all([
        supabase.from('blog_posts').select('id, created_at, title, published, last_optimized_at', { count: 'exact' }),
        supabase.from('trek_packages').select('id, created_at, name, updated_at', { count: 'exact' }),
        supabase.from('booking_inquiries').select('id, created_at, name, status', { count: 'exact' }),
        supabase.from('testimonials').select('id, created_at, name', { count: 'exact' }),
        supabase.from('seo_optimization_logs').select('id, blog_post_id, status, created_at').order('created_at', { ascending: false }).limit(20)
      ]);

      if (blogsRes.error) throw blogsRes.error;
      if (treksRes.error) throw treksRes.error;
      if (bookingsRes.error) throw bookingsRes.error;
      if (testimonialsRes.error) throw testimonialsRes.error;
      if (seoLogsRes.error) throw seoLogsRes.error;

      const blogs = blogsRes.data || [];
      const treks = treksRes.data || [];
      const bookings = bookingsRes.data || [];
      const testimonials = testimonialsRes.data || [];
      const seoLogs = seoLogsRes.data || [];

      // Calculate metrics
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      
      const publishedThisMonth = blogs.filter(blog => {
        const blogDate = new Date(blog.created_at);
        return blog.published && 
               blogDate.getMonth() === currentMonth && 
               blogDate.getFullYear() === currentYear;
      }).length;

      const optimizedContent = blogs.filter(blog => blog.last_optimized_at).length;

      setContentMetrics({
        totalBlogs: blogsRes.count || 0,
        totalTreks: treksRes.count || 0,
        totalBookings: bookingsRes.count || 0,
        totalTestimonials: testimonialsRes.count || 0,
        publishedThisMonth,
        optimizedContent
      });

      // Build recent activity
      const activities: RecentActivity[] = [];
      
      // Add recent blogs
      blogs.slice(0, 5).forEach(blog => {
        activities.push({
          id: blog.id,
          type: 'blog',
          title: blog.title,
          date: blog.created_at,
          status: blog.published ? 'published' : 'draft'
        });
      });

      // Add recent treks
      treks.slice(0, 3).forEach(trek => {
        activities.push({
          id: trek.id,
          type: 'trek',
          title: trek.name,
          date: trek.created_at,
          status: 'active'
        });
      });

      // Add recent bookings
      bookings.slice(0, 5).forEach(booking => {
        activities.push({
          id: booking.id,
          type: 'booking',
          title: `Booking from ${booking.name}`,
          date: booking.created_at,
          status: booking.status
        });
      });

      // Add recent optimizations
      seoLogs.slice(0, 5).forEach(log => {
        activities.push({
          id: log.id,
          type: 'optimization',
          title: 'SEO Optimization',
          date: log.created_at,
          status: log.status
        });
      });

      // Sort by date and take most recent
      activities.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      setRecentActivity(activities.slice(0, 10));

      // Create top content (simulated data based on real content)
      const topContentData: TopContent[] = [
        ...blogs.slice(0, 3).map((blog, index) => ({
          id: blog.id,
          title: blog.title,
          type: 'blog' as const,
          views: Math.floor(Math.random() * 2000) + 500,
          engagement: Math.floor(Math.random() * 50) + 10
        })),
        ...treks.slice(0, 2).map((trek, index) => ({
          id: trek.id,
          title: trek.name,
          type: 'trek' as const,
          views: Math.floor(Math.random() * 1500) + 300,
          engagement: Math.floor(Math.random() * 40) + 15
        }))
      ];

      topContentData.sort((a, b) => b.views - a.views);
      setTopContent(topContentData);

    } catch (error) {
      console.error('Failed to load analytics data:', error);
      toast({
        title: "Error",
        description: "Failed to load analytics data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadAnalyticsData();
    const interval = setInterval(loadAnalyticsData, 60000); // Refresh every minute
    return () => clearInterval(interval);
  }, [loadAnalyticsData]);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'blog': return <FileText className="h-4 w-4" />;
      case 'trek': return <Mountain className="h-4 w-4" />;
      case 'booking': return <Users className="h-4 w-4" />;
      case 'testimonial': return <MessageSquare className="h-4 w-4" />;
      case 'optimization': return <Target className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'active': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-orange-100 text-orange-800';
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'success': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <BarChart3 className="h-8 w-8" />
            Analytics & Performance
          </h2>
          <p className="text-muted-foreground">
            Monitor content performance, user engagement, and system metrics
          </p>
        </div>
        <Button onClick={loadAnalyticsData} variant="outline" size="sm" disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Content</p>
                <p className="text-2xl font-bold">{contentMetrics.totalBlogs + contentMetrics.totalTreks}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                {contentMetrics.totalBlogs} blogs, {contentMetrics.totalTreks} treks
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Published This Month</p>
                <p className="text-2xl font-bold text-green-600">{contentMetrics.publishedThisMonth}</p>
              </div>
              <Calendar className="h-8 w-8 text-green-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                New content published
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">SEO Optimized</p>
                <p className="text-2xl font-bold text-purple-600">{contentMetrics.optimizedContent}</p>
              </div>
              <Target className="h-8 w-8 text-purple-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                AI-optimized content
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Inquiries</p>
                <p className="text-2xl font-bold text-orange-600">{contentMetrics.totalBookings}</p>
              </div>
              <Users className="h-8 w-8 text-orange-500" />
            </div>
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">
                Customer inquiries
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="content">Content Performance</TabsTrigger>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Content Growth
                </CardTitle>
                <CardDescription>Content creation and optimization trends</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Blog Posts</span>
                    <div className="flex items-center gap-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${Math.min((contentMetrics.totalBlogs / 50) * 100, 100)}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{contentMetrics.totalBlogs}</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Trek Packages</span>
                    <div className="flex items-center gap-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-600 h-2 rounded-full" 
                          style={{ width: `${Math.min((contentMetrics.totalTreks / 20) * 100, 100)}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{contentMetrics.totalTreks}</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">SEO Optimized</span>
                    <div className="flex items-center gap-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-purple-600 h-2 rounded-full" 
                          style={{ width: `${Math.min((contentMetrics.optimizedContent / contentMetrics.totalBlogs) * 100, 100)}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{contentMetrics.optimizedContent}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Top Performing Content
                </CardTitle>
                <CardDescription>Most viewed content this month</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {topContent.slice(0, 5).map((content, index) => (
                    <div key={content.id} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-muted-foreground">#{index + 1}</span>
                        {content.type === 'blog' ? (
                          <FileText className="h-4 w-4 text-blue-500" />
                        ) : (
                          <Mountain className="h-4 w-4 text-green-500" />
                        )}
                        <span className="text-sm truncate max-w-48">{content.title}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{content.views} views</Badge>
                        <Badge variant="secondary">{content.engagement}% engagement</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Blog Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Total Posts</span>
                    <Badge variant="outline">{contentMetrics.totalBlogs}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Published</span>
                    <Badge variant="outline" className="text-green-600">
                      {contentMetrics.totalBlogs - (contentMetrics.totalBlogs * 0.1)}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">SEO Optimized</span>
                    <Badge variant="outline" className="text-purple-600">{contentMetrics.optimizedContent}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mountain className="h-5 w-5" />
                  Trek Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Total Packages</span>
                    <Badge variant="outline">{contentMetrics.totalTreks}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Featured</span>
                    <Badge variant="outline" className="text-blue-600">
                      {Math.floor(contentMetrics.totalTreks * 0.3)}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Avg. Views</span>
                    <Badge variant="outline">1,250</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Engagement
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Total Inquiries</span>
                    <Badge variant="outline">{contentMetrics.totalBookings}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Testimonials</span>
                    <Badge variant="outline" className="text-green-600">{contentMetrics.totalTestimonials}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Conversion Rate</span>
                    <Badge variant="outline">3.2%</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Activity
              </CardTitle>
              <CardDescription>Latest content updates and user interactions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {getActivityIcon(activity.type)}
                      <div>
                        <p className="text-sm font-medium">{activity.title}</p>
                        <p className="text-xs text-muted-foreground">{formatDate(activity.date)}</p>
                      </div>
                    </div>
                    <Badge className={getStatusColor(activity.status)}>
                      {activity.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AnalyticsPerformanceTab;
